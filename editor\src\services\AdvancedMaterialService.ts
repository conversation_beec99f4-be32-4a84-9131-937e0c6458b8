/**
 * 高级材质系统服务
 * 提供PBR材质、程序化材质生成、材质编辑等功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 材质类型枚举
export enum MaterialType {
  STANDARD = 'standard',
  PBR = 'pbr',
  UNLIT = 'unlit',
  TOON = 'toon',
  GLASS = 'glass',
  METAL = 'metal',
  FABRIC = 'fabric',
  SKIN = 'skin',
  WATER = 'water',
  EMISSIVE = 'emissive'
}

// 纹理类型枚举
export enum TextureType {
  DIFFUSE = 'diffuse',
  NORMAL = 'normal',
  ROUGHNESS = 'roughness',
  METALLIC = 'metallic',
  SPECULAR = 'specular',
  EMISSION = 'emission',
  OCCLUSION = 'occlusion',
  HEIGHT = 'height',
  OPACITY = 'opacity',
  SUBSURFACE = 'subsurface'
}

// 材质属性接口
export interface MaterialProperties {
  // 基础属性
  name: string;
  type: MaterialType;
  
  // 颜色属性
  albedo: [number, number, number];
  emission: [number, number, number];
  emissionIntensity: number;
  
  // PBR属性
  metallic: number;
  roughness: number;
  specular: number;
  specularTint: number;
  
  // 透明度
  opacity: number;
  alphaMode: 'OPAQUE' | 'MASK' | 'BLEND';
  alphaCutoff: number;
  
  // 法线和高度
  normalScale: number;
  heightScale: number;
  
  // 环境遮蔽
  occlusionStrength: number;
  
  // 次表面散射
  subsurfaceRadius: [number, number, number];
  subsurfaceColor: [number, number, number];
  
  // 各向异性
  anisotropy: number;
  anisotropyRotation: number;
  
  // 清漆层
  clearcoat: number;
  clearcoatRoughness: number;
  
  // 光泽
  sheen: number;
  sheenTint: number;
  
  // 双面渲染
  doubleSided: boolean;
  
  // 自定义属性
  customProperties: Record<string, any>;
}

// 纹理信息接口
export interface TextureInfo {
  id: string;
  type: TextureType;
  url: string;
  width: number;
  height: number;
  format: string;
  wrapS: 'REPEAT' | 'CLAMP_TO_EDGE' | 'MIRRORED_REPEAT';
  wrapT: 'REPEAT' | 'CLAMP_TO_EDGE' | 'MIRRORED_REPEAT';
  magFilter: 'NEAREST' | 'LINEAR';
  minFilter: 'NEAREST' | 'LINEAR' | 'NEAREST_MIPMAP_NEAREST' | 'LINEAR_MIPMAP_NEAREST' | 'NEAREST_MIPMAP_LINEAR' | 'LINEAR_MIPMAP_LINEAR';
  generateMipmaps: boolean;
  flipY: boolean;
  offset: [number, number];
  scale: [number, number];
  rotation: number;
}

// 材质定义接口
export interface MaterialDefinition {
  id: string;
  properties: MaterialProperties;
  textures: Map<TextureType, TextureInfo>;
  shaderCode?: {
    vertex?: string;
    fragment?: string;
  };
  metadata: MaterialMetadata;
  timestamp: number;
}

// 材质元数据接口
export interface MaterialMetadata {
  author: string;
  description: string;
  tags: string[];
  category: string;
  version: string;
  license: string;
  thumbnail?: string;
  previewScene?: string;
  complexity: number; // 0-100
  performance: number; // 0-100
  quality: number; // 0-100
}

// 程序化材质参数接口
export interface ProceduralMaterialParams {
  type: 'noise' | 'pattern' | 'gradient' | 'cellular' | 'fractal';
  seed: number;
  scale: number;
  octaves: number;
  persistence: number;
  lacunarity: number;
  colors: [number, number, number][];
  colorStops: number[];
  customParams: Record<string, any>;
}

// 材质预设接口
export interface MaterialPreset {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail: string;
  properties: Partial<MaterialProperties>;
  textures: Partial<Record<TextureType, string>>;
  tags: string[];
}

// 材质库接口
export interface MaterialLibrary {
  id: string;
  name: string;
  description: string;
  materials: MaterialDefinition[];
  presets: MaterialPreset[];
  version: string;
  author: string;
  license: string;
}

/**
 * 高级材质系统服务类
 */
export class AdvancedMaterialService extends EventEmitter {
  private static instance: AdvancedMaterialService;
  private materials: Map<string, MaterialDefinition> = new Map();
  private libraries: Map<string, MaterialLibrary> = new Map();
  private presets: Map<string, MaterialPreset> = new Map();
  private textureCache: Map<string, any> = new Map();

  private constructor() {
    super();
    this.initializeDefaultMaterials();
    this.initializeDefaultPresets();
  }

  public static getInstance(): AdvancedMaterialService {
    if (!AdvancedMaterialService.instance) {
      AdvancedMaterialService.instance = new AdvancedMaterialService();
    }
    return AdvancedMaterialService.instance;
  }

  /**
   * 初始化默认材质
   */
  private initializeDefaultMaterials(): void {
    // 标准PBR材质
    const standardPBR = this.createMaterial({
      name: 'Standard PBR',
      type: MaterialType.PBR,
      albedo: [0.8, 0.8, 0.8],
      emission: [0, 0, 0],
      emissionIntensity: 0,
      metallic: 0,
      roughness: 0.5,
      specular: 0.5,
      specularTint: 0,
      opacity: 1,
      alphaMode: 'OPAQUE',
      alphaCutoff: 0.5,
      normalScale: 1,
      heightScale: 0.1,
      occlusionStrength: 1,
      subsurfaceRadius: [0, 0, 0],
      subsurfaceColor: [1, 1, 1],
      anisotropy: 0,
      anisotropyRotation: 0,
      clearcoat: 0,
      clearcoatRoughness: 0,
      sheen: 0,
      sheenTint: 0,
      doubleSided: false,
      customProperties: {}
    });

    // 金属材质
    const metalMaterial = this.createMaterial({
      name: 'Metal',
      type: MaterialType.METAL,
      albedo: [0.7, 0.7, 0.8],
      emission: [0, 0, 0],
      emissionIntensity: 0,
      metallic: 1,
      roughness: 0.1,
      specular: 0.9,
      specularTint: 0,
      opacity: 1,
      alphaMode: 'OPAQUE',
      alphaCutoff: 0.5,
      normalScale: 1,
      heightScale: 0.1,
      occlusionStrength: 1,
      subsurfaceRadius: [0, 0, 0],
      subsurfaceColor: [1, 1, 1],
      anisotropy: 0,
      anisotropyRotation: 0,
      clearcoat: 0,
      clearcoatRoughness: 0,
      sheen: 0,
      sheenTint: 0,
      doubleSided: false,
      customProperties: {}
    });

    // 玻璃材质
    const glassMaterial = this.createMaterial({
      name: 'Glass',
      type: MaterialType.GLASS,
      albedo: [1, 1, 1],
      emission: [0, 0, 0],
      emissionIntensity: 0,
      metallic: 0,
      roughness: 0,
      specular: 0.9,
      specularTint: 0,
      opacity: 0.1,
      alphaMode: 'BLEND',
      alphaCutoff: 0.5,
      normalScale: 1,
      heightScale: 0.1,
      occlusionStrength: 1,
      subsurfaceRadius: [0, 0, 0],
      subsurfaceColor: [1, 1, 1],
      anisotropy: 0,
      anisotropyRotation: 0,
      clearcoat: 1,
      clearcoatRoughness: 0,
      sheen: 0,
      sheenTint: 0,
      doubleSided: true,
      customProperties: {}
    });

    this.materials.set(standardPBR.id, standardPBR);
    this.materials.set(metalMaterial.id, metalMaterial);
    this.materials.set(glassMaterial.id, glassMaterial);
  }

  /**
   * 初始化默认预设
   */
  private initializeDefaultPresets(): void {
    const presets: MaterialPreset[] = [
      {
        id: 'preset_chrome',
        name: 'Chrome',
        description: 'Shiny chrome metal material',
        category: 'metals',
        thumbnail: '/presets/chrome.jpg',
        properties: {
          albedo: [0.95, 0.95, 0.95],
          metallic: 1,
          roughness: 0.05
        },
        textures: {},
        tags: ['metal', 'chrome', 'shiny']
      },
      {
        id: 'preset_gold',
        name: 'Gold',
        description: 'Polished gold material',
        category: 'metals',
        thumbnail: '/presets/gold.jpg',
        properties: {
          albedo: [1, 0.8, 0.2],
          metallic: 1,
          roughness: 0.1
        },
        textures: {},
        tags: ['metal', 'gold', 'precious']
      },
      {
        id: 'preset_wood',
        name: 'Wood',
        description: 'Natural wood material',
        category: 'organic',
        thumbnail: '/presets/wood.jpg',
        properties: {
          albedo: [0.6, 0.4, 0.2],
          metallic: 0,
          roughness: 0.8
        },
        textures: {
          [TextureType.DIFFUSE]: '/textures/wood_diffuse.jpg',
          [TextureType.NORMAL]: '/textures/wood_normal.jpg'
        },
        tags: ['wood', 'organic', 'natural']
      },
      {
        id: 'preset_fabric',
        name: 'Fabric',
        description: 'Soft fabric material',
        category: 'fabric',
        thumbnail: '/presets/fabric.jpg',
        properties: {
          albedo: [0.8, 0.8, 0.9],
          metallic: 0,
          roughness: 0.9,
          sheen: 0.5
        },
        textures: {},
        tags: ['fabric', 'cloth', 'soft']
      }
    ];

    presets.forEach(preset => {
      this.presets.set(preset.id, preset);
    });
  }

  /**
   * 创建材质
   */
  public createMaterial(properties: MaterialProperties): MaterialDefinition {
    const materialId = `material_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const material: MaterialDefinition = {
      id: materialId,
      properties,
      textures: new Map(),
      metadata: {
        author: 'User',
        description: properties.name,
        tags: [properties.type],
        category: 'custom',
        version: '1.0.0',
        license: 'MIT',
        complexity: this.calculateComplexity(properties),
        performance: this.calculatePerformance(properties),
        quality: this.calculateQuality(properties)
      },
      timestamp: Date.now()
    };

    this.materials.set(materialId, material);
    this.emit('materialCreated', material);
    
    return material;
  }

  /**
   * 更新材质
   */
  public updateMaterial(materialId: string, updates: Partial<MaterialProperties>): MaterialDefinition {
    const material = this.materials.get(materialId);
    if (!material) {
      throw new Error('Material not found');
    }

    material.properties = { ...material.properties, ...updates };
    material.metadata.complexity = this.calculateComplexity(material.properties);
    material.metadata.performance = this.calculatePerformance(material.properties);
    material.metadata.quality = this.calculateQuality(material.properties);
    material.timestamp = Date.now();

    this.emit('materialUpdated', material);
    return material;
  }

  /**
   * 添加纹理到材质
   */
  public addTextureToMaterial(materialId: string, textureType: TextureType, textureInfo: TextureInfo): void {
    const material = this.materials.get(materialId);
    if (!material) {
      throw new Error('Material not found');
    }

    material.textures.set(textureType, textureInfo);
    material.timestamp = Date.now();

    this.emit('materialTextureAdded', { material, textureType, textureInfo });
  }

  /**
   * 生成程序化材质
   */
  public async generateProceduralMaterial(
    baseProperties: Partial<MaterialProperties>,
    params: ProceduralMaterialParams
  ): Promise<MaterialDefinition> {
    // 模拟程序化生成延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    const properties: MaterialProperties = {
      name: `Procedural ${params.type}`,
      type: MaterialType.PBR,
      albedo: [0.5, 0.5, 0.5],
      emission: [0, 0, 0],
      emissionIntensity: 0,
      metallic: 0,
      roughness: 0.5,
      specular: 0.5,
      specularTint: 0,
      opacity: 1,
      alphaMode: 'OPAQUE',
      alphaCutoff: 0.5,
      normalScale: 1,
      heightScale: 0.1,
      occlusionStrength: 1,
      subsurfaceRadius: [0, 0, 0],
      subsurfaceColor: [1, 1, 1],
      anisotropy: 0,
      anisotropyRotation: 0,
      clearcoat: 0,
      clearcoatRoughness: 0,
      sheen: 0,
      sheenTint: 0,
      doubleSided: false,
      customProperties: { proceduralParams: params },
      ...baseProperties
    };

    // 根据程序化参数调整属性
    switch (params.type) {
      case 'noise':
        properties.roughness = 0.3 + (Math.random() * 0.4);
        break;
      case 'pattern':
        properties.metallic = Math.random() * 0.5;
        break;
      case 'gradient':
        properties.albedo = params.colors[0] || [0.5, 0.5, 0.5];
        break;
    }

    const material = this.createMaterial(properties);
    
    // 生成程序化纹理
    await this.generateProceduralTextures(material.id, params);
    
    this.emit('proceduralMaterialGenerated', material);
    return material;
  }

  /**
   * 生成程序化纹理
   */
  private async generateProceduralTextures(materialId: string, params: ProceduralMaterialParams): Promise<void> {
    // 模拟纹理生成
    const textureTypes = [TextureType.DIFFUSE, TextureType.NORMAL, TextureType.ROUGHNESS];
    
    for (const textureType of textureTypes) {
      const textureInfo: TextureInfo = {
        id: `texture_${Date.now()}_${textureType}`,
        type: textureType,
        url: `data:image/png;base64,${this.generateTextureData(params, textureType)}`,
        width: 512,
        height: 512,
        format: 'RGBA',
        wrapS: 'REPEAT',
        wrapT: 'REPEAT',
        magFilter: 'LINEAR',
        minFilter: 'LINEAR_MIPMAP_LINEAR',
        generateMipmaps: true,
        flipY: false,
        offset: [0, 0],
        scale: [1, 1],
        rotation: 0
      };

      this.addTextureToMaterial(materialId, textureType, textureInfo);
    }
  }

  /**
   * 生成纹理数据
   */
  private generateTextureData(params: ProceduralMaterialParams, textureType: TextureType): string {
    // 简化的纹理数据生成（实际应该使用更复杂的算法）
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    const ctx = canvas.getContext('2d')!;

    // 根据参数和纹理类型生成不同的图案
    switch (params.type) {
      case 'noise':
        this.generateNoiseTexture(ctx, params, textureType);
        break;
      case 'pattern':
        this.generatePatternTexture(ctx, params, textureType);
        break;
      case 'gradient':
        this.generateGradientTexture(ctx, params, textureType);
        break;
      default:
        this.generateDefaultTexture(ctx, textureType);
    }

    return canvas.toDataURL().split(',')[1];
  }

  /**
   * 生成噪声纹理
   */
  private generateNoiseTexture(ctx: CanvasRenderingContext2D, _params: ProceduralMaterialParams, textureType: TextureType): void {
    const imageData = ctx.createImageData(512, 512);
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
      const noise = Math.random();
      const value = Math.floor(noise * 255);
      
      if (textureType === TextureType.NORMAL) {
        // 法线贴图需要特殊处理
        data[i] = 128 + (noise - 0.5) * 100;     // R
        data[i + 1] = 128 + (noise - 0.5) * 100; // G
        data[i + 2] = 255;                        // B (Z向上)
      } else {
        data[i] = value;     // R
        data[i + 1] = value; // G
        data[i + 2] = value; // B
      }
      data[i + 3] = 255; // A
    }

    ctx.putImageData(imageData, 0, 0);
  }

  /**
   * 生成图案纹理
   */
  private generatePatternTexture(ctx: CanvasRenderingContext2D, _params: ProceduralMaterialParams, textureType: TextureType): void {
    ctx.fillStyle = '#808080';
    ctx.fillRect(0, 0, 512, 512);

    ctx.fillStyle = textureType === TextureType.NORMAL ? '#8080ff' : '#ffffff';
    
    // 生成棋盘图案
    const tileSize = 64;
    for (let x = 0; x < 512; x += tileSize * 2) {
      for (let y = 0; y < 512; y += tileSize * 2) {
        ctx.fillRect(x, y, tileSize, tileSize);
        ctx.fillRect(x + tileSize, y + tileSize, tileSize, tileSize);
      }
    }
  }

  /**
   * 生成渐变纹理
   */
  private generateGradientTexture(ctx: CanvasRenderingContext2D, params: ProceduralMaterialParams, textureType: TextureType): void {
    const gradient = ctx.createLinearGradient(0, 0, 512, 0);
    
    if (params.colors && params.colors.length > 0) {
      params.colors.forEach((color, index) => {
        const stop = index / (params.colors.length - 1);
        const r = Math.floor(color[0] * 255);
        const g = Math.floor(color[1] * 255);
        const b = Math.floor(color[2] * 255);
        gradient.addColorStop(stop, `rgb(${r},${g},${b})`);
      });
    } else {
      gradient.addColorStop(0, '#000000');
      gradient.addColorStop(1, '#ffffff');
    }

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 512, 512);
  }

  /**
   * 生成默认纹理
   */
  private generateDefaultTexture(ctx: CanvasRenderingContext2D, textureType: TextureType): void {
    if (textureType === TextureType.NORMAL) {
      ctx.fillStyle = '#8080ff'; // 法线贴图的默认颜色
    } else {
      ctx.fillStyle = '#808080'; // 灰色
    }
    ctx.fillRect(0, 0, 512, 512);
  }

  /**
   * 应用材质预设
   */
  public applyPreset(materialId: string, presetId: string): MaterialDefinition {
    const material = this.materials.get(materialId);
    const preset = this.presets.get(presetId);

    if (!material) {
      throw new Error('Material not found');
    }

    if (!preset) {
      throw new Error('Preset not found');
    }

    // 应用预设属性
    if (preset.properties) {
      material.properties = { ...material.properties, ...preset.properties };
    }

    // 应用预设纹理
    if (preset.textures) {
      Object.entries(preset.textures).forEach(([textureType, url]) => {
        if (url) {
          const textureInfo: TextureInfo = {
            id: `texture_${Date.now()}_${textureType}`,
            type: textureType as TextureType,
            url,
            width: 1024,
            height: 1024,
            format: 'RGBA',
            wrapS: 'REPEAT',
            wrapT: 'REPEAT',
            magFilter: 'LINEAR',
            minFilter: 'LINEAR_MIPMAP_LINEAR',
            generateMipmaps: true,
            flipY: false,
            offset: [0, 0],
            scale: [1, 1],
            rotation: 0
          };
          material.textures.set(textureType as TextureType, textureInfo);
        }
      });
    }

    material.timestamp = Date.now();
    this.emit('presetApplied', { material, preset });

    return material;
  }

  /**
   * 克隆材质
   */
  public cloneMaterial(materialId: string, newName?: string): MaterialDefinition {
    const originalMaterial = this.materials.get(materialId);
    if (!originalMaterial) {
      throw new Error('Material not found');
    }

    const clonedProperties = {
      ...originalMaterial.properties,
      name: newName || `${originalMaterial.properties.name} Copy`
    };

    const clonedMaterial = this.createMaterial(clonedProperties);

    // 克隆纹理
    originalMaterial.textures.forEach((textureInfo, textureType) => {
      const clonedTextureInfo = { ...textureInfo, id: `texture_${Date.now()}_${textureType}` };
      clonedMaterial.textures.set(textureType, clonedTextureInfo);
    });

    // 克隆元数据
    clonedMaterial.metadata = {
      ...originalMaterial.metadata,
      author: 'User',
      description: `Cloned from ${originalMaterial.properties.name}`
    };

    this.emit('materialCloned', { original: originalMaterial, cloned: clonedMaterial });
    return clonedMaterial;
  }

  /**
   * 删除材质
   */
  public deleteMaterial(materialId: string): boolean {
    const material = this.materials.get(materialId);
    if (!material) {
      return false;
    }

    this.materials.delete(materialId);
    this.emit('materialDeleted', material);
    return true;
  }

  /**
   * 获取材质
   */
  public getMaterial(materialId: string): MaterialDefinition | undefined {
    return this.materials.get(materialId);
  }

  /**
   * 获取所有材质
   */
  public getAllMaterials(): MaterialDefinition[] {
    return Array.from(this.materials.values());
  }

  /**
   * 搜索材质
   */
  public searchMaterials(query: string, filters?: {
    type?: MaterialType;
    category?: string;
    tags?: string[];
  }): MaterialDefinition[] {
    const materials = this.getAllMaterials();

    return materials.filter(material => {
      // 文本搜索
      const matchesQuery = !query ||
        material.properties.name.toLowerCase().includes(query.toLowerCase()) ||
        material.metadata.description.toLowerCase().includes(query.toLowerCase()) ||
        material.metadata.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()));

      // 类型过滤
      const matchesType = !filters?.type || material.properties.type === filters.type;

      // 分类过滤
      const matchesCategory = !filters?.category || material.metadata.category === filters.category;

      // 标签过滤
      const matchesTags = !filters?.tags || filters.tags.every(tag =>
        material.metadata.tags.includes(tag)
      );

      return matchesQuery && matchesType && matchesCategory && matchesTags;
    });
  }

  /**
   * 获取材质预设
   */
  public getPresets(category?: string): MaterialPreset[] {
    const presets = Array.from(this.presets.values());

    if (category) {
      return presets.filter(preset => preset.category === category);
    }

    return presets;
  }

  /**
   * 添加自定义预设
   */
  public addPreset(preset: MaterialPreset): void {
    this.presets.set(preset.id, preset);
    this.emit('presetAdded', preset);
  }

  /**
   * 导入材质库
   */
  public async importLibrary(libraryData: MaterialLibrary): Promise<void> {
    // 验证库数据
    if (!libraryData.id || !libraryData.materials) {
      throw new Error('Invalid library data');
    }

    // 导入材质
    libraryData.materials.forEach(material => {
      this.materials.set(material.id, material);
    });

    // 导入预设
    if (libraryData.presets) {
      libraryData.presets.forEach(preset => {
        this.presets.set(preset.id, preset);
      });
    }

    this.libraries.set(libraryData.id, libraryData);
    this.emit('libraryImported', libraryData);
  }

  /**
   * 导出材质库
   */
  public exportLibrary(materialIds: string[], libraryInfo: {
    name: string;
    description: string;
    author: string;
    license: string;
  }): MaterialLibrary {
    const materials = materialIds.map(id => this.materials.get(id)).filter(Boolean) as MaterialDefinition[];

    const library: MaterialLibrary = {
      id: `library_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: libraryInfo.name,
      description: libraryInfo.description,
      materials,
      presets: [],
      version: '1.0.0',
      author: libraryInfo.author,
      license: libraryInfo.license
    };

    return library;
  }

  /**
   * 计算材质复杂度
   */
  private calculateComplexity(properties: MaterialProperties): number {
    let complexity = 0;

    // 基础复杂度
    complexity += 20;

    // PBR属性增加复杂度
    if (properties.type === MaterialType.PBR) {
      complexity += 30;
    }

    // 透明度增加复杂度
    if (properties.alphaMode !== 'OPAQUE') {
      complexity += 20;
    }

    // 次表面散射增加复杂度
    if (properties.subsurfaceRadius.some(v => v > 0)) {
      complexity += 25;
    }

    // 各向异性增加复杂度
    if (properties.anisotropy > 0) {
      complexity += 15;
    }

    // 清漆层增加复杂度
    if (properties.clearcoat > 0) {
      complexity += 15;
    }

    return Math.min(100, complexity);
  }

  /**
   * 计算材质性能
   */
  private calculatePerformance(properties: MaterialProperties): number {
    let performance = 100;

    // 复杂材质类型降低性能
    if (properties.type === MaterialType.GLASS || properties.type === MaterialType.WATER) {
      performance -= 20;
    }

    // 透明度降低性能
    if (properties.alphaMode === 'BLEND') {
      performance -= 15;
    }

    // 次表面散射降低性能
    if (properties.subsurfaceRadius.some(v => v > 0)) {
      performance -= 25;
    }

    // 各向异性降低性能
    if (properties.anisotropy > 0) {
      performance -= 10;
    }

    // 双面渲染降低性能
    if (properties.doubleSided) {
      performance -= 10;
    }

    return Math.max(0, performance);
  }

  /**
   * 计算材质质量
   */
  private calculateQuality(properties: MaterialProperties): number {
    let quality = 50;

    // PBR材质提高质量
    if (properties.type === MaterialType.PBR) {
      quality += 30;
    }

    // 合理的粗糙度值提高质量
    if (properties.roughness >= 0.1 && properties.roughness <= 0.9) {
      quality += 10;
    }

    // 法线贴图提高质量
    if (properties.normalScale > 0) {
      quality += 10;
    }

    // 环境遮蔽提高质量
    if (properties.occlusionStrength > 0) {
      quality += 10;
    }

    return Math.min(100, quality);
  }

  /**
   * 获取材质统计信息
   */
  public getMaterialStats(): {
    totalMaterials: number;
    materialsByType: Record<MaterialType, number>;
    materialsByCategory: Record<string, number>;
    averageComplexity: number;
    averagePerformance: number;
    averageQuality: number;
  } {
    const materials = this.getAllMaterials();

    const materialsByType = {} as Record<MaterialType, number>;
    const materialsByCategory = {} as Record<string, number>;
    let totalComplexity = 0;
    let totalPerformance = 0;
    let totalQuality = 0;

    materials.forEach(material => {
      // 按类型统计
      materialsByType[material.properties.type] = (materialsByType[material.properties.type] || 0) + 1;

      // 按分类统计
      materialsByCategory[material.metadata.category] = (materialsByCategory[material.metadata.category] || 0) + 1;

      // 累计指标
      totalComplexity += material.metadata.complexity;
      totalPerformance += material.metadata.performance;
      totalQuality += material.metadata.quality;
    });

    return {
      totalMaterials: materials.length,
      materialsByType,
      materialsByCategory,
      averageComplexity: materials.length > 0 ? totalComplexity / materials.length : 0,
      averagePerformance: materials.length > 0 ? totalPerformance / materials.length : 0,
      averageQuality: materials.length > 0 ? totalQuality / materials.length : 0
    };
  }

  /**
   * 清除纹理缓存
   */
  public clearTextureCache(): void {
    this.textureCache.clear();
    this.emit('textureCacheCleared');
  }

  /**
   * 获取支持的材质类型
   */
  public getSupportedMaterialTypes(): MaterialType[] {
    return Object.values(MaterialType);
  }

  /**
   * 获取支持的纹理类型
   */
  public getSupportedTextureTypes(): TextureType[] {
    return Object.values(TextureType);
  }
}

export default AdvancedMaterialService;
